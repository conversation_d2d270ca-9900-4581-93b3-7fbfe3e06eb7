export const en = {
  common: {
    successResult: 'this process is sucess',
  },
  pages: {
    dashboard: {
      title: 'Dashboard',
      teams: 'Teams',
      agents: 'Agents',
      categories: 'Categories',
      managers: 'Managers',
      createManager: 'Create Manager',
      editManager: 'Edit Manager',
      preferences: 'Preferences',
      pageOne: 'Page One',
      overView: 'Overview',
    },
    profile: {
      pageTitle: 'Profile',
      knowledgeBase: 'Knowledge Base',
      settings: 'Settings',
    },
    auth: {
      signIn: 'Sign in',
      signUp: 'Sign up',
      resetPassword: 'Reset Password',
      newPassword: 'New Password',
    },
    error: {
      notFoundTitle: '404 page not found!',
    },
  },
  auth: {
    welcome: 'Welcome to Midad!',
    welcomeSubtitle:
      'Access your personalized dashboard, track your workflows, and manage your tasks.',
    joinUs: 'Join us and start automating your workflows!',
    joinUsSubtitle:
      'Sign up today to gain access to our platform, manage automations, and unlock the power of seamless task automation.',
    resetPassword: 'Reset your password',
    resetPasswordSubtitle: 'Enter your email and we will send you a link to reset your password.',
    setNewPassword: 'Set new password',
    setNewPasswordSubtitle: 'Create a new password for your account.',
    newPasswordView: {
      title: 'Set New Password',
      subtitle: "Enter your email, and we'll send you a link to reset your password.",
      newPassword: 'New Password',
      newPasswordPlaceholder: 'Enter your new password',
      confirmPassword: 'Confirm Password',
      confirmPasswordPlaceholder: 'Confirm your new password',
      changePassword: 'Change Password',
      passwordRequirements: {
        minLength: 'Minimum 8 characters long',
        hasUpperLower: 'Must include uppercase, lowercase, and a number',
        hasSpecial: 'Must contain at least one special character',
        noSpaces: 'No spaces before or after the password',
      },
      footerLinks: {
        privacyPolicy: 'Privacy policy',
        termsOfUse: 'Terms of use',
        dmca: 'DMCA',
      },
    },
    forgotPassword: 'Forgot password?',
    rememberPassword: 'Remember password?',
    login: 'Login',
    register: 'Register',
    alreadyHaveAccount: 'Already have an account?',
    orRegisterWith: 'Or register with',
    orLoginWith: 'Or login with',
    email: 'Email',
    password: 'Password',
    name: 'Name',
    enterYourEmail: 'Enter your email',
    enterYourPassword: 'Enter your password',
    enterYourName: 'Enter your name',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    enterYourNewPassword: 'Enter your new password',
    confirmYourNewPassword: 'Confirm your new password',
    changePassword: 'Change Password',
    send: 'Send',
    passwordResetSuccess: 'Password changed successfully!',
    passwordValidation: {
      minLength: 'Must be at least 8 characters',
      hasUpperLower: 'Must include uppercase and lowercase letters',
      hasNumber: 'Must include at least one number',
      hasSpecial: 'Must include at least one special character',
      noSpaces: 'Must not have spaces at beginning or end',
      passwordsMatch: 'Passwords must match',
    },
    codeSent: "We've sent a 5-digit code to your email {email}.",
    resetPasswordView: {
      title: 'Reset password',
      subtitle: "Enter your email, and we'll send you a link to reset your password.",
      emailLabel: 'Email',
      emailPlaceholder: 'Enter your email',
      sendButton: 'Send',
      rememberPassword: 'Remember password?',
      loginLink: 'Login',
      codeSentMessage: "We've sent a 5-digit code to your email {email}",
      enterCodeMessage: 'Please enter it below.',
      checkingButton: 'Checking...',
      checking: 'Checking...',
      codeVerified: 'Code verified successfully! Redirecting to password change page...',
      footerLinks: {
        privacyPolicy: 'Privacy policy',
        termsOfUse: 'Terms of use',
        dmca: 'DMCA',
      },
    },
  },
  error: {
    notFound: 'Sorry, page not found!',
    notFoundDescription:
      "Sorry, we couldn't find the page you're looking for. Perhaps you've mistyped the URL? Be sure to check your spelling.",
    forbidden: 'No permission',
    forbiddenDescription:
      "The page you're trying to access has restricted access. Please refer to your system administrator.",
    serverError: '500 Internal server error',
    serverErrorDescription: 'There was an error, please try again later.',
    permissionDenied: 'Permission denied',
    permissionDeniedDescription: 'You do not have permission to access this page.',
    goToHome: 'Go to home',
  },
  components: {
    searchNotFound: {
      enterKeywords: 'Please enter keywords',
      notFound: 'Not found',
      noResults: 'No results found for',
      checkTypos: 'Try checking for typos or using complete words.',
    },
    noData: {
      noDataAvailable: 'No data available',
    },
    notifications: {
      title: 'Notifications',
      markAllAsRead: 'Mark all as read',
      viewAll: 'View all',
      markAsRead: 'Mark as read',
      today: 'Today',
      yesterday: 'Yesterday',
      older: 'Older',
      noNotifications: 'No notifications',
      noNotificationsDescription:
        "You'll receive notifications for important updates and activity.",
    },
    accountMenu: {
      profile: 'Profile',
      dashboard: 'Dashboard',
      settings: 'Settings',
      logout: 'Logout',
    },
    dialogs: {
      confirmDeleteMessage: 'Are you sure?',
      deleteTeam: 'Delete Team?',
      deleteTeamConfirm: 'Are you sure you want to delete this team?',
      logout: 'Logout',
      logoutConfirm: 'Are you sure you want to logout?',
    },
    buttons: {
      delete: 'Delete',
      cancel: 'Cancel',
      save: 'Save',
      next: 'Next',
      previous: 'Previous',
      create: 'Create',
      update: 'Update',
      createNewTeam: 'Create New Team',
      clearAll: 'Clear All',
    },
    tables: {
      rowsPerPageLabel: 'Rows per page',
      dense: 'Dense',
    },
    common: {
      successResult: 'Completed successfully',
      view: 'View',
      edit: 'Edit',
      search: 'Search...',
      searchFiles: 'Search files...',
      searchServices: 'Search services...',
      member: 'member',
      members: 'members',
      created: 'Created',
      privacyPolicy: 'Privacy policy',
      termsOfUse: 'Terms of use',
      dmca: 'DMCA',
    },
    dashboard: {
      overView: 'Overview',
      categories: 'Categories',
      teams: 'Teams',
    },
    user: {
      title: 'Hello',
    },
    header: {
      workForces: 'workForces',
    },
    navigation: {
      overview: 'Overview',
      categories: 'Categories',
      agents: 'Agents',
      teams: 'Teams',
      managers: 'Managers',
      users: 'Users',
      settings: 'Settings',
      home: 'Home',
      projects: 'Projects',
      subscription: 'Subscription',
      security: 'Security',
      accountSettings: 'Account settings',
    },
    agents: {
      adminTemplates: 'Admin Templates',
      usersTemplates: 'User Templates',
    },
    search: {
      placeholder: 'Search...',
      searchFiles: 'Search files...',
      searchServices: 'Search services...',
    },
    workspaces: {
      free: 'Free',
      pro: 'Pro',
      team1: 'Team 1',
      team2: 'Team 2',
      team3: 'Team 3',
    },
    profile: {
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      username: 'Username',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      keepCurrentPassword: 'Leave blank to keep current password',
      profileUpdated: 'Profile updated successfully!',
      settings: {
        language: 'Language',
        chooseLanguage: 'Choose your preferred language',
        managePreferences: 'Manage your account settings and preferences',
        theme: 'Theme',
        chooseTheme: 'Choose your preferred theme',
        light: 'Light',
        dark: 'Dark',
        system: 'System',
        languageOptions: {
          english: 'English',
          arabic: 'Arabic',
        },
      },
      validation: {
        firstNameRequired: 'First name is required',
        lastNameRequired: 'Last name is required',
        emailValid: 'Email must be a valid email address',
        usernameLength: 'Username must be at least 3 characters',
        passwordLength: 'Password must be at least 8 characters',
        passwordUppercase: 'Password must contain at least one uppercase letter',
        passwordLowercase: 'Password must contain at least one lowercase letter',
        passwordNumber: 'Password must contain at least one number',
        passwordsMatch: "Passwords don't match",
      },
    },
    teams: {
      title: 'Teams',
      form: {
        editTeam: 'Edit Team',
        createNewTeam: 'Create New Team',
        formInstructions: 'Please fill out the required fields in each steps',
        steps: {
          teamInfo: 'Team Info',
          resources: 'Resources',
          aiModel: 'AI Model',
          members: 'Members',
          instructions: 'Instructions',
        },
        fields: {
          teamName: 'Team Name',
          description: 'Description',
          instructions: 'Instructions',
        },
      },
      resources: {
        searchFolders: 'Search folders...',
        searchFiles: 'Search files in this folder...',
        searchAiModels: 'Search AI models...',
        searchMembers: 'Search members...',
        selectedFiles: 'Selected Files',
        chooseAiModel: 'Choose the AI model',
        chooseTeamMembers: 'Choose the team members',
      },
    },
  },
  settings: {
    title: 'Settings',
    reset: 'Reset',
    close: 'Close',
    darkMode: 'Dark mode',
    contrast: 'Contrast',
    rtl: 'Right to left',
    compact: 'Compact',
    compactTooltip: 'Dashboard only and available at large resolutions > 1600px (xl)',
    nav: 'Nav',
    navTooltip: 'Dashboard only',
    layout: 'Layout',
    color: 'Color',
    font: 'Font',
    presets: 'Presets',
    exit: 'Exit',
    fullScreen: 'Full Screen',
  },
  categories: {
    title: 'Categories',
    dashboard: 'Dashboard',
    createNew: 'Create New Category',
    noCategories: 'No categories found',
    createFirst: 'Create your first category to get started',
    tryAgain: 'Please try again later',
    table: {
      name: 'Name',
      dateCreated: 'Date Created',
      agentsCount: 'Agents Count',
      action: 'Action',
      edit: 'Edit',
      delete: 'Delete',
    },
  },
};
