import { Box, Container, Typography, useTheme } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { AppContainer } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { useTranslation } from 'react-i18next';
import CategoryForm from '../form/category-form';
import { useCategoriesView } from '../hooks/use-categories-view';
import { CategoryFormValues } from '../form/category-schema';

// ----------------------------------------------------------------------

const CreateCategoryView = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  // Use our custom hook for categories
  const { handleCreateCategory, isLoading } = useCategoriesView();

  // Handle form submission
  const handleFormSubmit = async (data: CategoryFormValues) => {
    try {
      await handleCreateCategory(data);
      // Navigate back to categories list on success
      navigate(paths.dashboard.categories.root);
    } catch (error) {
      console.error('Failed to create category:', error);
    }
  };

  // Handle cancel - navigate back to categories list
  const handleCancel = () => {
    navigate(paths.dashboard.categories.root);
  };

  return (
    <AppContainer
      title={t('categories.createNew')}
      pageTitle={t('categories.createNew')}
      routeLinks={[
        {
          name: t('categories.title'),
          href: paths.dashboard.categories.root,
        },
        {
          name: t('categories.createNew'),
        },
      ]}

    >
   <Container>
            <Box
              sx={{
                p: 3,
                border: '1px solid',
                borderColor: 'divider',
                backgroundColor: 'divider',
                borderRadius: 1,
              }}
            >
            
        <Box
          sx={{
            bgcolor: 'background.paper',
            borderRadius: 3,
            p: 4,
            boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
            border: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Box sx={{ mb: 4, textAlign: 'center' }}>
            <Typography variant="h4" sx={{ mb: 1 }}>
              {t('categories.createNew')}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {t('categories.createDescription')}
            </Typography>
          </Box>

          <CategoryForm
            onSubmit={handleFormSubmit}
            onCancel={handleCancel}
            loading={isLoading}
          />
        </Box>
        </Box>
          </Container>
    </AppContainer>
  );
};

export default CreateCategoryView;
